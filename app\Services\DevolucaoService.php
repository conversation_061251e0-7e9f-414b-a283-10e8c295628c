<?php

namespace App\Services;
use NFePHP\NFe\Make;
use NFePHP\NFe\Tools;
use NFePHP\Common\Certificate;
use NFePHP\NFe\Common\Standardize;
use App\Models\Venda;
use App\Models\Devolucao;
use App\Models\ConfigNota;
use App\Models\Certificado;
use NFePHP\NFe\Complements;
use NFePHP\DA\NFe\Danfe;
use NFePHP\DA\Legacy\FilesFolders;
use NFePHP\Common\Soap\SoapCurl;
use App\Models\Tributacao;
use App\Models\ConfigSystem;

error_reporting(E_ALL);
ini_set('display_errors', 'On');

class DevolucaoService{

	private $config;
	private $tools;
	protected $empresa_id = null;
	protected $timeout = 8;

	public function __construct($config, $modelo){
		//$json = json_encode($config);
		$value = session('user_logged');
		$this->empresa_id = $value['empresa'];
		$certificado = Certificado::
		where('empresa_id', $this->empresa_id)
		->first();
		// print_r($certificado->arquivo);
		$this->config = $config;
		$this->tools = new Tools(json_encode($config), Certificate::readPfx($certificado->arquivo, $certificado->senha));
		$soapCurl = new SoapCurl();
		$soapCurl->httpVersion('1.1');
		$this->tools->loadSoapClass($soapCurl);
		$this->tools->model($modelo);
		$config = ConfigSystem::first();
		if($config){
			if($config->timeout_nfe){
				$this->timeout = $config->timeout_nfe;
			}
		}
	}

	private function validate_EAN13Barcode($ean)
	{

		$sumEvenIndexes = 0;
		$sumOddIndexes  = 0;

		$eanAsArray = array_map('intval', str_split($ean));

		if(substr($ean, 0, 1) == 1){
			return false;
		}

		if(strlen($ean) == 14){
			return true;
		}

		if (!$this->has13Numbers($eanAsArray)) {
			return false;
		};

		for ($i = 0; $i < count($eanAsArray)-1; $i++) {
			if ($i % 2 === 0) {
				$sumOddIndexes  += $eanAsArray[$i];
			} else {
				$sumEvenIndexes += $eanAsArray[$i];
			}
		}

		$rest = ($sumOddIndexes + (3 * $sumEvenIndexes)) % 10;

		if ($rest !== 0) {
			$rest = 10 - $rest;
		}

		return $rest === $eanAsArray[12];
	}

	private function has13Numbers(array $ean)
	{
		return count($ean) === 13;
	}

	public function gerarDevolucao($devolucao){

		$config = ConfigNota::
		where('empresa_id', $this->empresa_id)
		->first(); // iniciando os dados do emitente NF

		$tributacao = Tributacao::
		where('empresa_id', $this->empresa_id)
		->first(); // iniciando tributos

		$regime = $config->regime_tributario ?? 1; // Adicionando definição da variável regime

		$nfe = new Make();
		$stdInNFe = new \stdClass();
		$stdInNFe->versao = '4.00';
		$stdInNFe->Id = null;
		$stdInNFe->pk_nItem = '';

		$infNFe = $nfe->taginfNFe($stdInNFe);

		$vendaLast = Venda::lastNF();
		$lastNumero = $vendaLast > 0 ? $vendaLast : 0;

		$stdIde = new \stdClass();
		$stdIde->cUF = $config->cUF;
		$stdIde->cNF = rand(11111,99999);
		// $stdIde->natOp = $venda->natureza->natureza;
		$stdIde->natOp = $devolucao->natureza->natureza;

		// $stdIde->indPag = 1; //NÃO EXISTE MAIS NA VERSÃO 4.00 // forma de pagamento

		$stdIde->mod = 55;
		$stdIde->serie = $config->numero_serie_nfe;
		$stdIde->nNF = (int)$lastNumero+1;
		$stdIde->dhEmi = date("Y-m-d\TH:i:sP");
		$stdIde->dhSaiEnt = date("Y-m-d\TH:i:sP");
		$stdIde->tpNF = $devolucao->tipo;
		$stdIde->idDest = $config->UF != $devolucao->fornecedor->cidade->uf ? 2 : 1;
		$stdIde->cMunFG = $config->codMun;
		// $stdIde->tpImp = 1;
		$stdIde->tpImp = $config->tipo_impressao_danfe;
		$stdIde->tpEmis = 1;
		$stdIde->cDV = 0;
		$stdIde->tpAmb = $config->ambiente;
		$stdIde->finNFe = $devolucao->natureza->finNFe; // 4 - devolucao
		$stdIde->indFinal = 1;
		$stdIde->indPres = 1;
		if($config->ambiente == 2){
			$stdIde->indIntermed = 0;
		}
		$stdIde->procEmi = '0';
		$stdIde->verProc = '3.10.31';

		$tagide = $nfe->tagide($stdIde);

		$stdEmit = new \stdClass();
		$stdEmit->xNome = $config->razao_social;
		$stdEmit->xFant = $config->nome_fantasia;

		$ie = preg_replace('/[^0-9]/', '', $config->ie);
		$stdEmit->IE = $ie;
		$stdEmit->CRT = $tributacao->getCRT();

		$cnpj = preg_replace('/[^0-9]/', '', $config->cnpj);
		if(strlen($cnpj) == 14){
			$stdEmit->CNPJ = $cnpj;
		}else{
			$stdEmit->CPF = $cnpj;
		}

		$emit = $nfe->tagemit($stdEmit);

		// ENDERECO EMITENTE
		$stdEnderEmit = new \stdClass();
		$stdEnderEmit->xLgr = $config->logradouro;
		$stdEnderEmit->nro = $config->numero;
		$stdEnderEmit->xCpl = "";
		$stdEnderEmit->xBairro = $config->bairro;
		$stdEnderEmit->cMun = $config->codMun;
		$stdEnderEmit->xMun = $config->municipio;
		$stdEnderEmit->UF = $config->UF;

		$cep = preg_replace('/[^0-9]/', '', $config->cep);
		$stdEnderEmit->CEP = $cep;
		$stdEnderEmit->cPais = $config->codPais;
		$stdEnderEmit->xPais = $config->pais;

		$enderEmit = $nfe->tagenderEmit($stdEnderEmit);

		// DESTINATARIO
		$stdDest = new \stdClass();
		$stdDest->xNome = $devolucao->fornecedor->razao_social;

		if($devolucao->fornecedor->ie_rg == 'ISENTO'){
			$stdDest->indIEDest = "2";
		}else{
			$stdDest->indIEDest = "1";
		}

		$cnpj_cpf = preg_replace('/[^0-9]/', '', $devolucao->fornecedor->cpf_cnpj);

		if(strlen($cnpj_cpf) == 14){
			$stdDest->CNPJ = $cnpj_cpf;
			$ie = preg_replace('/[^0-9]/', '', $devolucao->fornecedor->ie_rg);
			$stdDest->IE = $ie;
		}else{
			$stdDest->CPF = $cnpj_cpf;
			if($devolucao->fornecedor->ie_rg != 'ISENTO'){
				$stdDest->IE = $devolucao->fornecedor->ie_rg;
			}
		}

		$dest = $nfe->tagdest($stdDest);

		$stdEnderDest = new \stdClass();
		$stdEnderDest->xLgr = $devolucao->fornecedor->rua;
		$stdEnderDest->nro = $devolucao->fornecedor->numero;
		$stdEnderDest->xCpl = "";
		$stdEnderDest->xBairro = $devolucao->fornecedor->bairro;
		$stdEnderDest->cMun = $devolucao->fornecedor->cidade->codigo;
		$stdEnderDest->xMun = strtoupper($this->retiraAcentos($devolucao->fornecedor->cidade->nome));
		$stdEnderDest->UF = $devolucao->fornecedor->cidade->uf;

		$cep = str_replace("-", "", $devolucao->fornecedor->cep);
		$cep = str_replace(".", "", $cep);
		$stdEnderDest->CEP = $cep;
		$stdEnderDest->cPais = "1058";
		$stdEnderDest->xPais = "BRASIL";

		$enderDest = $nfe->tagenderDest($stdEnderDest);

		$somaProdutos = 0;
		$somaICMS = 0;
		$somaIPI = 0;
		$somaST = 0;
		//PRODUTOS
		$itemCont = 0;

		$totalItens = count($devolucao->itens);
		$somaFrete = 0;
		$somaAcrescimo = 0;

		$std = new \stdClass();
		$std->refNFe = $devolucao->chave_nf_entrada;
		$nfe->tagrefNFe($std);

		$VBC = 0;
		$somaDesconto = 0;

		foreach($devolucao->itens as $i){
			$itemCont++;

			$stdProd = new \stdClass();
			$stdProd->item = $itemCont;

			$cod = $this->validate_EAN13Barcode($i->codBarras);

			$stdProd->cEAN = $cod ? $i->codBarras : 'SEM GTIN';
			$stdProd->cEANTrib = $cod ? $i->codBarras : 'SEM GTIN';

			$stdProd->cProd = $i->cod;
			$stdProd->xProd = $i->nome;

			if($i->cBenef){
				$stdProd->cBenef = $i->cBenef;
			}

			$ncm = $i->ncm;
			$ncm = str_replace(".", "", $ncm);
			$stdProd->NCM = $ncm;

			if($devolucao->tipo == 1){
				$stdProd->CFOP = $config->UF != $devolucao->fornecedor->cidade->uf ?
				$devolucao->natureza->CFOP_saida_inter_estadual : $devolucao->natureza->CFOP_saida_estadual;
			}else{
				$stdProd->CFOP = $config->UF != $devolucao->fornecedor->cidade->uf ?
				$devolucao->natureza->CFOP_entrada_inter_estadual : $devolucao->natureza->CFOP_entrada_estadual;
			}

			$stdProd->uCom = $i->unidade_medida;
			$stdProd->qCom = $this->format($i->quantidade, 4);
			$stdProd->vUnCom = $this->format($i->valor_unit);
			$stdProd->vProd = $this->format($i->quantidade * $i->valor_unit);
			$stdProd->uTrib = $i->unidade_medida;
			$stdProd->qTrib = $this->format($i->quantidade, 4);
			$stdProd->vUnTrib = $this->format($i->valor_unit);
			$stdProd->indTot = 1;

			$somaProdutos += $stdProd->vProd;

			if($devolucao->vDesc > 0.01 && $somaDesconto < $devolucao->vDesc){
				if($itemCont < sizeof($devolucao->itens)){
					$totalVenda = $devolucao->valor_devolvido;
					$media = (((($stdProd->vProd - $totalVenda)/$totalVenda))*100);
					$media = 100 - ($media * -1);
					$tempDesc = $this->format(($devolucao->vDesc*$media)/100);

					if($tempDesc > 0.01){
						$somaDesconto += $tempDesc;
						$stdProd->vDesc = $this->format($tempDesc, $config->casas_decimais);
					}else{
						$somaDesconto = $devolucao->vDesc;
						$stdProd->vDesc = $this->format($somaDesconto, $config->casas_decimais);
					}
				}else{
					if(($devolucao->vDesc - $somaDesconto) > 0.01){
						$stdProd->vDesc = $this->format($devolucao->vDesc - $somaDesconto, $config->casas_decimais);
					}
				}
			}

			$prod = $nfe->tagprod($stdProd);

			//TAG IMPOSTO
			$stdImposto = new \stdClass();
			$stdImposto->item = $itemCont;
			$imposto = $nfe->tagimposto($stdImposto);

			// ICMS
			if($i->perc_iss == 0){
				// regime normal
				if($regime == 1){
					$stdICMS = new \stdClass();
					$stdICMS->item = $itemCont;
					$stdICMS->orig = $i->origem ?? 0;
					$stdICMS->CST = $i->cst_csosn ?? '40';
					$stdICMS->modBC = $i->modBC ?? 3;
					$stdICMS->vBC = $stdProd->vProd + ($stdProd->vFrete ?? 0) + ($stdProd->vOutro ?? 0) - ($stdProd->vDesc ?? 0);
					$stdICMS->pICMS = $this->format($i->perc_icms ?? 0);
					$stdICMS->vICMS = $stdICMS->vBC * (($stdICMS->pICMS ?? 0)/100);

					if($stdICMS->CST != '40' && $stdICMS->CST != '41' && $stdICMS->pICMS > 0){
						$VBC += $stdICMS->vBC;
						$somaICMS += $stdICMS->vICMS;
					}

					if($i->cst_csosn == '60'){
						$ICMS = $nfe->tagICMSST($stdICMS);
					}else{
						$ICMS = $nfe->tagICMS($stdICMS);
					}
				}else{
					// regime simples
					$stdICMS = new \stdClass();
					$stdICMS->item = $itemCont;
					$stdICMS->orig = $i->origem ?? 0;
					$stdICMS->CSOSN = $i->cst_csosn ?? '102';

					if($i->cst_csosn == '61'){
						$stdICMS->CST = $i->cst_csosn;
						$stdICMS->qBCMonoRet = $this->format($stdProd->qTrib);
						$stdICMS->adRemICMSRet = $this->format($i->adRemICMSRet ?? 0, 4);
						$stdICMS->vICMSMonoRet = $this->format(($i->adRemICMSRet ?? 0)*$stdProd->qTrib, 4);
						$ICMS = $nfe->tagICMS($stdICMS);
					}else{
						$ICMS = $nfe->tagICMSSN($stdICMS);
					}

					if(($i->perc_icms ?? 0) > 0 && $stdICMS->CSOSN == '900'){
						$stdICMS->modBC = 3;
						$stdICMS->vBC = $stdProd->vProd + ($stdProd->vFrete ?? 0);
						$stdICMS->pICMS = $this->format($i->perc_icms);
						$stdICMS->vICMS = $stdICMS->vBC * (($stdICMS->pICMS)/100);
						$VBC += $stdICMS->vBC;
						$somaICMS += $stdICMS->vICMS;
					}
				}
			} else {
				// ISS
				$stdICMS = new \stdClass();
				$stdICMS->item = $itemCont;
				$stdICMS->orig = $i->origem ?? 0;
				$stdICMS->CST = '40';
				$ICMS = $nfe->tagICMS($stdICMS);
			}

			// PIS
			$stdPIS = new \stdClass();
			$stdPIS->item = $itemCont;
			$stdPIS->CST = $i->cst_pis ?? '07';

			if($i->perc_pis > 0){
				$stdPIS->vBC = $this->format($stdProd->vProd - ($stdProd->vDesc ?? 0));
				$stdPIS->pPIS = $this->format($i->perc_pis);
				$stdPIS->vPIS = $this->format($stdPIS->vBC * ($i->perc_pis/100));
			}

			$PIS = $nfe->tagPIS($stdPIS);

			// COFINS
			$stdCOFINS = new \stdClass();
			$stdCOFINS->item = $itemCont;
			$stdCOFINS->CST = $i->cst_cofins ?? '07';

			if($i->perc_cofins > 0){
				$stdCOFINS->vBC = $this->format($stdProd->vProd - ($stdProd->vDesc ?? 0));
				$stdCOFINS->pCOFINS = $this->format($i->perc_cofins);
				$stdCOFINS->vCOFINS = $this->format($stdCOFINS->vBC * ($i->perc_cofins/100));
			}

			$COFINS = $nfe->tagCOFINS($stdCOFINS);

			// IPI se houver
			if($i->perc_ipi > 0){
				$stdIPI = new \stdClass();
				$stdIPI->item = $itemCont;
				$stdIPI->clEnq = null;
				$stdIPI->CNPJProd = null;
				$stdIPI->cSelo = null;
				$stdIPI->qSelo = null;
				$stdIPI->cEnq = '999';
				$stdIPI->CST = $i->cst_ipi ?? '99';
				$stdIPI->vBC = $this->format($stdProd->vProd);
				$stdIPI->pIPI = $this->format($i->perc_ipi);
				$stdIPI->vIPI = $this->format($stdIPI->vBC * ($i->perc_ipi/100));
				$somaIPI += $stdIPI->vIPI;

				$nfe->tagIPI($stdIPI);
			}
		}

		$stdICMSTot = new \stdClass();
		$stdICMSTot->vBC = $this->format($VBC);
		$stdICMSTot->vICMS = $this->format($somaICMS);
		$stdICMSTot->vICMSDeson = 0.00;
		$stdICMSTot->vBCST = 0.00;
		$stdICMSTot->vST = $this->format($somaST);
		$stdICMSTot->vProd = $this->format($somaProdutos);
		$stdICMSTot->vFrete = $devolucao->vFrete > 0 ? $devolucao->vFrete : $somaFrete;
		$stdICMSTot->vNF = $this->format($somaProdutos + $stdICMSTot->vFrete + $somaIPI - $devolucao->vDesc + $devolucao->despesa_acessorias + $somaST);
		$stdICMSTot->vSeg = 0.00;
		$stdICMSTot->vDesc = $this->format($devolucao->vDesc);
		$stdICMSTot->vII = 0.00;
		$stdICMSTot->vIPI = 0.00;
		$stdICMSTot->vPIS = 0.00;
		$stdICMSTot->vCOFINS = 0.00;
		$stdICMSTot->vOutro = $this->format($devolucao->despesa_acessorias);
		$stdICMSTot->vTotTrib = 0.00;
		$ICMSTot = $nfe->tagICMSTot($stdICMSTot);


		$stdTransp = new \stdClass();
		$stdTransp->modFrete = $devolucao->frete_tipo;

		$transp = $nfe->tagtransp($stdTransp);


		// if($devolucao->transportadora_nome != ''){
		// 	$std = new \stdClass();

		// 	$std->xNome = $devolucao->transportadora_nome;

		// 	$std->xEnder = $devolucao->transportadora_endereco;
		// 	$std->xMun = $devolucao->transportadora_cidade;
		// 	$std->UF = $devolucao->transportadora_uf;


		// 	$cnpj_cpf = $devolucao->transportadora_cpf_cnpj;
		// 	$cnpj_cpf = str_replace(".", "", $cnpj_cpf);
		// 	$cnpj_cpf = str_replace("/", "", $cnpj_cpf);
		// 	$cnpj_cpf = str_replace("-", "", $cnpj_cpf);

		// 	if(strlen($cnpj_cpf) == 14) $std->CNPJ = $cnpj_cpf;
		// 	else $std->CPF = $cnpj_cpf;

		// 	$nfe->tagtransporta($std);
		// }

		if($devolucao->transportadora_id != null){
			$std = new \stdClass();

			$std->xNome = $devolucao->transportadora->razao_social;

			$std->xEnder = $devolucao->transportadora->logradouro;
			$std->xMun = $this->retiraAcentos($devolucao->transportadora->cidade->nome);
			$std->UF = $devolucao->transportadora->cidade->uf;


			$cnpj_cpf = $devolucao->transportadora->cnpj_cpf;
			$cnpj_cpf = str_replace(".", "", $cnpj_cpf);
			$cnpj_cpf = str_replace("/", "", $cnpj_cpf);
			$cnpj_cpf = str_replace("-", "", $cnpj_cpf);

			if(strlen($cnpj_cpf) == 14) $std->CNPJ = $cnpj_cpf;
			else $std->CPF = $cnpj_cpf;

			$nfe->tagtransporta($std);
		}

		if($devolucao->veiculo_uf != '' && $devolucao->veiculo_placa != ''){
			$std = new \stdClass();

			$placa = str_replace("-", "", $devolucao->veiculo_placa);
			$std->placa = strtoupper($placa);
			$std->UF = $devolucao->veiculo_uf;

			$nfe->tagveicTransp($std);
		}


		if($devolucao->frete_peso_bruto > 0 && $devolucao->frete_peso_liquido > 0){

			$stdVol = new \stdClass();
			$stdVol->item = 1;
			$stdVol->qVol = $devolucao->frete_quantidade;
			$stdVol->esp = $devolucao->frete_especie;

			$stdVol->nVol = $devolucao->frete_numero;
			$stdVol->pesoL = $devolucao->frete_peso_liquido;
			$stdVol->pesoB = $devolucao->frete_peso_bruto;
			$vol = $nfe->tagvol($stdVol);
		}




		// $stdResp = new \stdClass();
		// $stdResp->CNPJ = '08543628000145';
		// $stdResp->xContato= 'Slym';
		// $stdResp->email = '<EMAIL>';
		// $stdResp->fone = '43996347016';

		// $nfe->taginfRespTec($stdResp);


	//Fatura

		// $stdFat = new \stdClass();
		// $stdFat->nFat = (int)$lastNumero+1;
		// $stdFat->vOrig = $this->format($somaProdutos);
		// $stdFat->vDesc = $this->format($venda->desconto);
		// $stdFat->vLiq = $this->format($somaProdutos-$venda->desconto);

		// $fatura = $nfe->tagfat($stdFat);


	//Duplicata

		// if(count($venda->duplicatas) > 0){
		// 	$contFatura = 1;
		// 	foreach($venda->duplicatas as $ft){
		// 		$stdDup = new \stdClass();
		// 		$stdDup->nDup = "00".$contFatura;
		// 		$stdDup->dVenc = substr($ft->data_vencimento, 0, 10);
		// 		$stdDup->vDup = $this->format($ft->valor_integral);

		// 		$nfe->tagdup($stdDup);
		// 		$contFatura++;
		// 	}
		// }else{
		// 	$stdDup = new \stdClass();
		// 	$stdDup->nDup = '001';
		// 	$stdDup->dVenc = Date('Y-m-d');
		// 	$stdDup->vDup =  $this->format($somaProdutos-$venda->desconto);

		// 	$nfe->tagdup($stdDup);
		// }

		$stdPag = new \stdClass();
		$pag = $nfe->tagpag($stdPag);

		$stdDetPag = new \stdClass();


		$stdDetPag->tPag = '90';
		$stdDetPag->vPag = 0.00;

		$stdDetPag->indPag = '0'; // sem pagamento

		$detPag = $nfe->tagdetPag($stdDetPag);

		$stdInfoAdic = new \stdClass();
		$stdInfoAdic->infCpl = $this->retiraAcentos($devolucao->observacao);

		$infoAdic = $nfe->taginfAdic($stdInfoAdic);

		if($config->aut_xml != ''){

			$std = new \stdClass();
			$cnpj = preg_replace('/[^0-9]/', '', $config->aut_xml);
			$std->CNPJ = $cnpj;
			$aut = $nfe->tagautXML($std);
		}

		$std = new \stdClass();
		$std->CNPJ = env('RESP_CNPJ'); //CNPJ da pessoa jurídica responsável pelo sistema utilizado na emissão do documento fiscal eletrônico
		$std->xContato = env('RESP_NOME'); //Nome da pessoa a ser contatada
		$std->email = env('RESP_EMAIL'); //E-mail da pessoa jurídica a ser contatada
		$std->fone = env('RESP_FONE'); //Telefone da pessoa jurídica/física a ser contatada

		// Validação dos campos obrigatórios do responsável técnico
		if (empty($std->CNPJ) || strlen($std->CNPJ) < 14) {
			throw new \Exception('CNPJ do responsável técnico é obrigatório e deve ter 14 dígitos');
		}
		if (empty($std->xContato) || strlen($std->xContato) < 2) {
			throw new \Exception('Nome do responsável técnico é obrigatório');
		}
		if (empty($std->email) || strlen($std->email) < 5 || !filter_var($std->email, FILTER_VALIDATE_EMAIL)) {
			throw new \Exception('Email do responsável técnico é obrigatório e deve ser válido');
		}
		if (empty($std->fone) || strlen($std->fone) < 10) {
			throw new \Exception('Telefone do responsável técnico é obrigatório e deve ter pelo menos 10 dígitos');
		}

		$nfe->taginfRespTec($std);

		// if($nfe->montaNFe()){
		// 	$arr = [
		// 		'chave' => $nfe->getChave(),
		// 		'xml' => $nfe->getXML(),
		// 		'nNf' => $stdIde->nNF
		// 	];
		// 	return $arr;
		// } else {
		// 	throw new Exception("Erro ao gerar NFe");
		// }

		try{
			$nfe->montaNFe();
			$arr = [
				'chave' => $nfe->getChave(),
				'xml' => $nfe->getXML(),
				'nNf' => $stdIde->nNF
			];
			return $arr;
		}catch(\Exception $e){
			return [
				'erros_xml' => $nfe->getErrors()
			];
		}

	}

	private function retiraAcentos($texto){
		return preg_replace(array("/(á|à|ã|â|ä)/","/(Á|À|Ã|Â|Ä)/","/(é|è|ê|ë)/","/(É|È|Ê|Ë)/","/(í|ì|î|ï)/","/(Í|Ì|Î|Ï)/","/(ó|ò|õ|ô|ö)/","/(Ó|Ò|Õ|Ô|Ö)/","/(ú|ù|û|ü)/","/(Ú|Ù|Û|Ü)/","/(ñ)/","/(Ñ)/", "/(ç)/", "/(Ç)/"),explode(" ","a A e E i I o O u U n N c C"),$texto);
	}

	public function sign($xml){
		return $this->tools->signNFe($xml);
	}

	public function transmitir($signXml, $chave, $devolucao_id = null){
		try{
			$idLote = str_pad(100, 15, '0', STR_PAD_LEFT);

			$resp = $this->tools->sefazEnviaLote([$signXml], $idLote);

			$st = new Standardize();
			$std = $st->toStd($resp);
			sleep($this->timeout);

			if ($std->cStat != 103) {
				return "Erro: [$std->cStat] - $std->xMotivo";
			}

			$recibo = $std->infRec->nRec;
			if($devolucao_id != null){
				$devolucao = Devolucao::where('id', $devolucao_id)->first();
				if($devolucao != null && (empty($devolucao->recibo) || $devolucao->recibo == null)){
					$devolucao->recibo = $recibo;
					$devolucao->save();
				}
			}

			$protocolo = $this->tools->sefazConsultaRecibo($recibo);
			sleep(1);

			$stdProtocolo = new Standardize();
			$protocoloStd = $stdProtocolo->toStd($protocolo);

			// Verifica se o protocolo foi autorizado
			if(isset($protocoloStd->protNFe->infProt->cStat) && $protocoloStd->protNFe->infProt->cStat == '100'){
				try {
					$xml = Complements::toAuthorize($signXml, $protocolo);
					file_put_contents(public_path('xml_devolucao/').$chave.'.xml',$xml);
					return $recibo;
				} catch (\Exception $e) {
					return "Erro: " . $stdProtocolo->toJson($protocolo);
				}
			} else {
				// Tratamento especial para rejeições específicas
				$cStat = $protocoloStd->protNFe->infProt->cStat ?? '';
				$xMotivo = $protocoloStd->protNFe->infProt->xMotivo ?? '';

				// Tratamento específico para duplicidade de NF-e
				if($cStat == '539') {
					return $this->tratarDuplicidadeNFe($xMotivo, $devolucao_id);
				}

				return "Erro: " . $stdProtocolo->toJson($protocolo);
			}

		} catch(\Exception $e){
			return "Erro: ".$e->getMessage();
		}
	}


	public function consultar($devolucao){
		try {

			$this->tools->model('55');

			$chave = $devolucao->chave_gerada;
			$response = $this->tools->sefazConsultaChave($chave);

			$stdCl = new Standardize($response);
			$arr = $stdCl->toArray();

			// $arr = json_decode($json);
			return json_encode($arr);

		} catch (\Exception $e) {
			echo $e->getMessage();
		}
	}

	public function format($number, $dec = 2){
		$number = (float) $number;
		return number_format($number, $dec, ".", "");
	}

	/**
	 * Trata especificamente o erro de duplicidade de NF-e (cStat 539)
	 */
	private function tratarDuplicidadeNFe($xMotivo, $devolucao_id = null) {
		try {
			// Extrair a chave da NF-e duplicada da mensagem
			preg_match('/chNFe:\s*(\d+)/', $xMotivo, $matches);
			$chaveDuplicada = $matches[1] ?? null;

			if(!$chaveDuplicada) {
				return "Erro: Duplicidade de NF-e detectada, mas não foi possível extrair a chave da mensagem: $xMotivo";
			}

			// Consultar informações da NF-e duplicada na SEFAZ
			$infoNFeDuplicada = $this->consultarNFePorChave($chaveDuplicada);

			// Buscar a devolução no banco de dados
			$devolucao = null;
			if($devolucao_id) {
				$devolucao = Devolucao::find($devolucao_id);
			}

			// Montar resposta detalhada
			$resposta = [
				'erro' => true,
				'cStat' => '539',
				'tipo' => 'duplicidade_nfe',
				'mensagem_original' => $xMotivo,
				'chave_duplicada' => $chaveDuplicada,
				'informacoes_nfe' => $infoNFeDuplicada,
				'sugestoes' => [
					'Verifique se já existe uma devolução para esta NF-e',
					'Consulte o histórico de devoluções da empresa',
					'Verifique se a NF-e de entrada está correta',
					'Entre em contato com o suporte se necessário'
				]
			];

			// Log detalhado para auditoria
			\Log::warning("Duplicidade de NF-e detectada", [
				'devolucao_id' => $devolucao_id,
				'chave_duplicada' => $chaveDuplicada,
				'empresa_id' => $devolucao ? $devolucao->empresa_id : null,
				'fornecedor_id' => $devolucao ? $devolucao->fornecedor_id : null,
				'data_registro' => $devolucao ? $devolucao->data_registro : null
			]);

			return json_encode($resposta, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

		} catch (\Exception $e) {
			\Log::error("Erro ao tratar duplicidade de NF-e: " . $e->getMessage());
			return "Erro ao processar duplicidade de NF-e: " . $e->getMessage();
		}
	}

	/**
	 * Consulta informações detalhadas de uma NF-e por chave
	 */
	private function consultarNFePorChave($chave) {
		try {
			$response = $this->tools->sefazConsultaChave($chave);
			$stdCl = new Standardize($response);
			$arr = $stdCl->toArray();

			// Extrair informações relevantes
			$info = [
				'chave' => $chave,
				'status' => $arr['xMotivo'] ?? 'Não disponível',
				'numero' => $arr['nNF'] ?? 'Não disponível',
				'serie' => $arr['serie'] ?? 'Não disponível',
				'data_emissao' => $arr['dhEmi'] ?? 'Não disponível',
				'valor_total' => $arr['vNF'] ?? 'Não disponível',
				'emitente' => [
					'nome' => $arr['xNome'] ?? 'Não disponível',
					'cnpj' => $arr['CNPJ'] ?? $arr['CPF'] ?? 'Não disponível'
				],
				'destinatario' => [
					'nome' => $arr['xNomeDest'] ?? 'Não disponível',
					'cnpj' => $arr['CNPJDest'] ?? $arr['CPFDest'] ?? 'Não disponível'
				]
			];

			return $info;

		} catch (\Exception $e) {
			\Log::error("Erro ao consultar NF-e por chave: " . $e->getMessage());
			return [
				'erro' => true,
				'mensagem' => 'Não foi possível consultar informações da NF-e: ' . $e->getMessage()
			];
		}
	}

	public function cancelar($devolucao, $justificativa){
		try {

			$chave = $devolucao->chave_gerada;
			$response = $this->tools->sefazConsultaChave($chave);
			$stdCl = new Standardize($response);
			$arr = $stdCl->toArray();
			sleep(2);
				// return $arr;
			$xJust = $justificativa;

			$nProt = $arr['protNFe']['infProt']['nProt'];

			$response = $this->tools->sefazCancela($chave, $xJust, $nProt);
			sleep(3);
			$stdCl = new Standardize($response);
			$std = $stdCl->toStd();
			$arr = $stdCl->toArray();
			$json = $stdCl->toJson();

			if ($std->cStat != 128) {
				// Retornar erro como JSON
				return json_encode([
					'erro' => true,
					'cStat' => $std->cStat,
					'xMotivo' => $std->xMotivo ?? 'Erro não especificado'
				]);
			} else {
				$cStat = $std->retEvento->infEvento->cStat;
				$public = env('SERVIDOR_WEB') ? 'public/' : '';
				if ($cStat == '101' || $cStat == '135' || $cStat == '155' ) {
					// SUCESSO PROTOCOLAR A SOLICITAÇÂO ANTES DE GUARDAR
					$xml = Complements::toAuthorize($this->tools->lastRequest, $response);
					file_put_contents($public.'xml_devolucao_cancelada/'.$chave.'.xml',$xml);

					return $json;
				} else {
					// Houve alguma falha no evento
					return $json;
				}
			}
		} catch (\Exception $e) {
			// Retornar erro como JSON em vez de string
			\Log::error("Erro ao cancelar devolução: " . $e->getMessage());
			return json_encode([
				'erro' => true,
				'mensagem' => 'Erro ao processar o cancelamento: ' . $e->getMessage()
			]);
		}
	}

	public function cartaCorrecao($devolucao, $correcao){

		try {

			$chave = $devolucao->chave_gerada;
			$xCorrecao = $correcao;
			$nSeqEvento = $devolucao->sequencia_cce+1;
			$response = $this->tools->sefazCCe($chave, $xCorrecao, $nSeqEvento);
			sleep(2);

			$stdCl = new Standardize($response);

			$std = $stdCl->toStd();

			$arr = $stdCl->toArray();

			$json = $stdCl->toJson();

			if ($std->cStat != 128) {
				// Retornar erro como JSON
				return json_encode([
					'erro' => true,
					'cStat' => $std->cStat,
					'xMotivo' => $std->xMotivo ?? 'Erro não especificado'
				]);
			} else {
				$cStat = $std->retEvento->infEvento->cStat;
				if ($cStat == '135' || $cStat == '136') {
					$public = env('SERVIDOR_WEB') ? 'public/' : '';
					// SUCESSO PROTOCOLAR A SOLICITAÇÂO ANTES DE GUARDAR
					$xml = Complements::toAuthorize($this->tools->lastRequest, $response);
					file_put_contents($public.'xml_devolucao_correcao/'.$chave.'.xml',$xml);

					$devolucao->sequencia_cce = $devolucao->sequencia_cce + 1;
					$devolucao->save();
					return $json;

				} else {
					// Houve alguma falha no evento
					return json_encode(['erro' => true, 'data' => $arr, 'status' => 402]);
				}
			}
		} catch (\Exception $e) {
			// Retornar erro como JSON em vez de string
			\Log::error("Erro ao processar carta de correção: " . $e->getMessage());
			return json_encode([
				'erro' => true,
				'mensagem' => 'Erro ao processar a carta de correção: ' . $e->getMessage()
			]);
		}
	}

}
