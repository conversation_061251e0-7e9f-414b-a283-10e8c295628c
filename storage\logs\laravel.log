[2025-07-29 13:23:24] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'empresa_id' cannot be null (Connection: mysql, SQL: insert into `erro_logs` (`arquivo`, `linha`, `erro`, `empresa_id`, `updated_at`, `created_at`) values (C:\Dev\www\gnfast\app\Http\Controllers\VendaCaixaController.php, 229, Attempt to read property "caixa_por_usuario" on null, ?, 2025-07-29 13:23:24, 2025-07-29 13:23:24)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'empresa_id' cannot be null (Connection: mysql, SQL: insert into `erro_logs` (`arquivo`, `linha`, `erro`, `empresa_id`, `updated_at`, `created_at`) values (C:\\Dev\\www\\gnfast\\app\\Http\\Controllers\\VendaCaixaController.php, 229, Attempt to read property \"caixa_por_usuario\" on null, ?, 2025-07-29 13:23:24, 2025-07-29 13:23:24)) at C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:801)
[stacktrace]
#0 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(755): Illuminate\\Database\\Connection->runQueryCallback('insert into `er...', Array, Object(Closure))
#1 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(569): Illuminate\\Database\\Connection->run('insert into `er...', Array, Object(Closure))
#2 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(533): Illuminate\\Database\\Connection->statement('insert into `er...', Array)
#3 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into `er...', Array)
#4 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3387): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `er...', Array, 'id')
#5 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1966): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#6 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1333): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1298): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1137): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1021): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(307): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\ErroLog))
#11 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1020): tap(Object(App\\Models\\ErroLog), Object(Closure))
#12 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2334): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2346): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 C:\\Dev\\www\\gnfast\\app\\Helpers\\User.php(153): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 C:\\Dev\\www\\gnfast\\app\\Http\\Controllers\\VendaCaixaController.php(705): __saveError(Object(ErrorException), NULL)
#17 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\VendaCaixaController->save(Object(Illuminate\\Http\\Request))
#18 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('save', Array)
#19 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\VendaCaixaController), 'save')
#20 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#21 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Routing\\Route->run()
#22 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Dev\\www\\gnfast\\app\\Http\\Controllers\\VendaCaixaController.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(162): App\\Http\\Controllers\\VendaCaixaController->App\\Http\\Controllers\\{closure}(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(777): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(741): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(730): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 C:\\Dev\\www\\gnfast\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 C:\\Dev\\www\\gnfast\\server.php(21): require_once('C:\\\\Dev\\\\www\\\\gnfa...')
#64 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'empresa_id' cannot be null at C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:580)
[stacktrace]
#0 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(580): PDOStatement->execute()
#1 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(788): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into `er...', Array)
#2 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(755): Illuminate\\Database\\Connection->runQueryCallback('insert into `er...', Array, Object(Closure))
#3 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(569): Illuminate\\Database\\Connection->run('insert into `er...', Array, Object(Closure))
#4 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(533): Illuminate\\Database\\Connection->statement('insert into `er...', Array)
#5 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into `er...', Array)
#6 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3387): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `er...', Array, 'id')
#7 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1966): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#8 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1333): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#9 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1298): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#10 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1137): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#11 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1021): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(307): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\ErroLog))
#13 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1020): tap(Object(App\\Models\\ErroLog), Object(Closure))
#14 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#15 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2334): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#16 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2346): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#17 C:\\Dev\\www\\gnfast\\app\\Helpers\\User.php(153): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#18 C:\\Dev\\www\\gnfast\\app\\Http\\Controllers\\VendaCaixaController.php(705): __saveError(Object(ErrorException), NULL)
#19 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\VendaCaixaController->save(Object(Illuminate\\Http\\Request))
#20 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('save', Array)
#21 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\VendaCaixaController), 'save')
#22 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#23 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Routing\\Route->run()
#24 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Dev\\www\\gnfast\\app\\Http\\Controllers\\VendaCaixaController.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(162): App\\Http\\Controllers\\VendaCaixaController->App\\Http\\Controllers\\{closure}(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#35 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(777): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#43 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(741): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#44 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(730): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#45 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#46 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#63 C:\\Dev\\www\\gnfast\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#64 C:\\Dev\\www\\gnfast\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#65 C:\\Dev\\www\\gnfast\\server.php(21): require_once('C:\\\\Dev\\\\www\\\\gnfa...')
#66 {main}
"} 
